// client/services/apiServices.ts

import { CarComplete } from "../../../types/models"
import { DailyAuctionResponse } from "../../../types/reports"

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'

async function request<T>(url: string, options?: RequestInit): Promise<T> {
    const res = await fetch(`${API_BASE}${url}`, {
        headers: { 'Content-Type': 'application/json' },
        ...options
    })

    const data = await res.json()

    if (!res.ok) throw new Error(data.error || 'API error')
    return data
}

export const api = {
    login: (username: string, password: string) =>
        request<{ session: any; user: any; auth_token: any }>('/auth/login', {
            method: 'POST',
            body: JSON.stringify({ username, password })
        }),
}


/**
 * ===================================
 * VEHICLES API
 * ===================================
 */
export const VEHICLES_API = {
    GET_ALL: async (authToken: string): Promise<CarComplete[]> => {
        return request<CarComplete[]>("/items/", {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });
    },
    CREATE_ITEM: async (data: any, authToken: string): Promise<any> => {
        return request("/items/", {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
    },
    // BY ID
    REMOVE_ITEM: async (carId: string, authToken: string): Promise<any> => {
        return request(`/items/${carId}`, {
            method: "DELETE",
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json',
            }
        });
    },
    PATCH_ITEM: async(carId: string, data: any ,authToken: string): Promise<any> => {
        return request(`/items/${carId}`, {
            method: 'PATCH',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
    }
}

/**
 * ===================================
 * REPORTS API
 * ===================================
 */
export const REPORTS_API = {
    GET_DAILY_AUCTION: async (date: string, authToken: string): Promise<DailyAuctionResponse> => {
        return request<DailyAuctionResponse>(`/reports/daily-auction?date=${date}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });
    }
}


/**
 * ===================================
 * MONTHLY SUMMARY API
 * ===================================
 */

export const MONTHLY_SUMMARY_APIS = {
    GET_MONTHLY_SOLD_CAR: async (month: number, year: number, authToken: string): Promise<MonthlySummary[]> => {
        
    }
}