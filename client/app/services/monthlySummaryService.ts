// client/app/services/monthlySummaryService.ts

import { MonthlySummary } from "../../../types/models"

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'

async function request<T>(url: string, options?: RequestInit): Promise<T> {
    const res = await fetch(`${API_BASE}${url}`, {
        headers: { 'Content-Type': 'application/json' },
        ...options
    })

    const data = await res.json()

    if (!res.ok) throw new Error(data.error || 'API error')
    return data
}

export interface MonthlySummaryParams {
    month: number
    year: number
}

export interface MonthlySummaryResponse {
    data: MonthlySummary[]
    totalCount: number
}

/**
 * ===================================
 * MONTHLY SUMMARY API
 * ===================================
 */
export const MONTHLY_SUMMARY_API = {
    /**
     * Get monthly summary data for sold cars
     * @param month Month number (1-12)
     * @param year Year in Gregorian format (converted from Buddhist year by subtracting 543)
     * @param authToken Authentication token
     * @returns Monthly summary data for cars sold in the specified month/year
     */
    GET_MONTHLY_SUMMARY: async (month: number, year: number, authToken: string): Promise<MonthlySummaryResponse> => {
        console.log("Fetching monthly summary for:", { month, year })
        return request<MonthlySummaryResponse>(`/api/reports/monthly-summary?month=${month}&year=${year}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });
    }
}
