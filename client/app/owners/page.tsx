"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/layout/header"
import { Sidebar } from "@/components/layout/sidebar"
import { Tabs, TabsContent } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar, FileDown, FileSpreadsheet, ArrowRight } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { MonthlySummaryTable } from "@/components/monthly-summary/monthly-summary-table"
import { MONTHLY_SUMMARY_API } from "../services/monthlySummaryService"
import type { MonthlySummary } from "../../../types/models"

export default function OwnersPage() {
  const router = useRouter()

  // Check if user is logged in
  useEffect(() => {
    const isLoggedIn = localStorage.getItem("isLoggedIn")
    if (!isLoggedIn) {
      router.push("/login")
    }
  }, [router])

  // Monthly summary state
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1) // Current month (1-12)
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear() + 543) // Current year in Buddhist format
  const [monthlySummaryData, setMonthlySummaryData] = useState<MonthlySummary[]>([])
  const [isLoading, setIsLoading] = useState(false)

  // Generate month options in Thai
  const months = [
    { value: 1, label: "มกราคม" },
    { value: 2, label: "กุมภาพันธ์" },
    { value: 3, label: "มีนาคม" },
    { value: 4, label: "เมษายน" },
    { value: 5, label: "พฤษภาคม" },
    { value: 6, label: "มิถุนายน" },
    { value: 7, label: "กรกฎาคม" },
    { value: 8, label: "สิงหาคม" },
    { value: 9, label: "กันยายน" },
    { value: 10, label: "ตุลาคม" },
    { value: 11, label: "พฤศจิกายน" },
    { value: 12, label: "ธันวาคม" },
  ]

  // Generate year options (last 5 years) in Buddhist format
  const currentYear = new Date().getFullYear() + 543 // Convert to Buddhist year
  const years = Array.from({ length: 5 }, (_, i) => currentYear - i)

  // Fetch monthly summary data
  const fetchMonthlySummary = async () => {
    setIsLoading(true)
    try {
      // Get auth token from localStorage
      const authToken = localStorage.getItem("token") || ""

      // Convert Buddhist year to Gregorian year for the API
      const gregorianYear = selectedYear - 543
      console.log("Buddhist Year:", selectedYear, "Gregorian Year:", gregorianYear)

      // Call the real API
      const response = await MONTHLY_SUMMARY_API.GET_MONTHLY_SUMMARY(selectedMonth, gregorianYear, authToken)
      setMonthlySummaryData(response.data)
    } catch (error) {
      console.error("Error fetching monthly summary:", error)
      // Set empty array on error
      setMonthlySummaryData([])
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch data on initial load and when month/year changes
  useEffect(() => {
    fetchMonthlySummary()
  }, [selectedMonth, selectedYear])

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex flex-1 flex-col">
        <Header />
        <main className="flex-1 overflow-auto p-6">
          <Tabs defaultValue="monthly" className="w-full">
            <div className="flex justify-between items-center mb-6">
              {/* Tab switching hidden - showing only Monthly Summary */}
            </div>


            {/* Monthly Summary Tab Content */}
            <TabsContent value="monthly" className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>Monthly Owner Summary</CardTitle>
                      <CardDescription>Overview of sold vehicles for the selected period</CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-5 w-5 text-muted-foreground" />
                      <span className="text-sm font-medium">
                        {months.find((m) => m.value === selectedMonth)?.label} {selectedYear}
                      </span>
                      {!isLoading && monthlySummaryData.length > 0 && (
                        <span className="ml-2 bg-primary text-white text-xs font-medium px-2 py-0.5 rounded-full">
                          {monthlySummaryData.length} Cars Sold
                        </span>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Date selection controls */}
                  <div className="flex flex-wrap gap-4 mb-6">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">Month:</span>
                      <Select
                        value={selectedMonth.toString()}
                        onValueChange={(value) => setSelectedMonth(Number.parseInt(value))}
                      >
                        <SelectTrigger className="w-[140px]">
                          <SelectValue placeholder="Select month" />
                        </SelectTrigger>
                        <SelectContent>
                          {months.map((month) => (
                            <SelectItem key={month.value} value={month.value.toString()}>
                              {month.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">Year:</span>
                      <Select
                        value={selectedYear.toString()}
                        onValueChange={(value) => setSelectedYear(Number.parseInt(value))}
                      >
                        <SelectTrigger className="w-[100px]">
                          <SelectValue placeholder="Select year" />
                        </SelectTrigger>
                        <SelectContent>
                          {years.map((year) => (
                            <SelectItem key={year} value={year.toString()}>
                              {year}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <Button variant="outline" onClick={fetchMonthlySummary} className="flex items-center gap-1">
                      <span>Enter</span>
                      <ArrowRight className="h-4 w-4" />
                    </Button>

                    <Button onClick={fetchMonthlySummary} className="ml-auto text-white" disabled={isLoading}>
                      {isLoading ? "Loading..." : "Generate Report"}
                    </Button>

                    <Button variant="outline" disabled={monthlySummaryData.length === 0}>
                      <FileSpreadsheet className="h-4 w-4 mr-2" />
                      Export Excel
                    </Button>

                    <Button variant="outline" disabled={monthlySummaryData.length === 0}>
                      <FileDown className="h-4 w-4 mr-2" />
                      Export PDF
                    </Button>
                  </div>

                  {/* Monthly summary table */}
                  <MonthlySummaryTable data={monthlySummaryData} isLoading={isLoading} />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  )
}
