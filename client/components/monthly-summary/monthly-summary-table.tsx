"use client"
import type { MonthlySummary } from "../../../types/models"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ScrollArea } from "@/components/ui/scroll-area"
import { formatCurrency, formatPercent } from "@/lib/utils"
import React from "react"

interface MonthlySummaryTableProps {
  data: MonthlySummary[]
  isLoading: boolean
}

// Define property groups for better organization
const propertyGroups = [
  {
    title: "Basic Information",
    properties: [
      { key: "car_number", label: "Car Number", format: (value: any) => value },
      { key: "tank_number", label: "Tank Number", format: (value: any) => value },
      { key: "brand", label: "Brand", format: (value: any) => value },
      { key: "purchase_date", label: "Purchase Date", format: (value: any) => new Date(value).toLocaleDateString() },
      { key: "sale_date", label: "Sale Date", format: (value: any) => new Date(value).toLocaleDateString() },
      { key: "time_in_stock", label: "Days in Stock", format: (value: any) => value },
    ],
  },
  {
    title: "Revenue",
    properties: [
      { key: "actual_selling_price", label: "Actual Selling Price", format: (value: any) => formatCurrency(value) },
      { key: "car_amount_finance", label: "Car Amount Finance", format: (value: any) => formatCurrency(value) },
      { key: "vat_on_car_amount", label: "VAT on Car Amount", format: (value: any) => formatCurrency(value) },
      { key: "car_commission_amount", label: "Car Commission Amount", format: (value: any) => formatCurrency(value) },
      { key: "total_revenue", label: "Total Revenue", format: (value: any) => formatCurrency(value) },
    ],
  },
  {
    title: "Investment",
    properties: [
      { key: "purchase_price", label: "Purchase Price", format: (value: any) => formatCurrency(value) },
      { key: "purchase_vat_percent", label: "Purchase VAT %", format: (value: any) => `${value}%` },
      {
        key: "operation_cost_incl_vat",
        label: "Operation Cost (incl. VAT)",
        format: (value: any) => formatCurrency(value),
      },
      {
        key: "transport_1_auction_lot",
        label: "Transport 1 (Auction Lot)",
        format: (value: any) => formatCurrency(value),
      },
      { key: "initial_check", label: "Initial Check", format: (value: any) => formatCurrency(value) },
      { key: "tax_insurance_cost_zero", label: "Tax Insurance Cost", format: (value: any) => formatCurrency(value) },
      { key: "other_costs_seven", label: "Other Costs", format: (value: any) => formatCurrency(value) },
      { key: "five_three_tax_percentage", label: "5.3% Tax", format: (value: any) => formatCurrency(value) },
      { key: "qc1_auction_lot", label: "QC1 Auction Lot", format: (value: any) => formatCurrency(value) },
      {
        key: "transport_2_personal_payment",
        label: "Transport 2 (Personal)",
        format: (value: any) => formatCurrency(value),
      },
      { key: "transport_3_tl_payment", label: "Transport 3 (TL)", format: (value: any) => formatCurrency(value) },
      { key: "total_investment", label: "Total Investment", format: (value: any) => formatCurrency(value) },
    ],
  },
  {
    title: "Tax",
    properties: [
      { key: "thirty_document", label: "30% Document", format: (value: any) => formatCurrency(value) },
      { key: "vat_percent", label: "VAT %", format: (value: any) => `${value}%` },
    ],
  },
  {
    title: "Commission and Fees",
    properties: [
      { key: "commission_s", label: "Commission S", format: (value: any) => formatCurrency(value) },
      { key: "commission_agent", label: "Commission Agent", format: (value: any) => formatCurrency(value) },
      { key: "commission_manager", label: "Commission Manager", format: (value: any) => formatCurrency(value) },
      { key: "qc_siriporn", label: "QC Siriporn", format: (value: any) => formatCurrency(value) },
      { key: "miscellaneous_siriporn", label: "Miscellaneous Siriporn", format: (value: any) => formatCurrency(value) },
      { key: "marketing_bak_cost", label: "Marketing BAK Cost", format: (value: any) => formatCurrency(value) },
      {
        key: "finance_payment_process",
        label: "Finance Payment Process",
        format: (value: any) => formatCurrency(value),
      },
      { key: "ems_registration_qc3", label: "EMS Registration QC3", format: (value: any) => formatCurrency(value) },
      { key: "registration_fee", label: "Registration Fee", format: (value: any) => formatCurrency(value) },
      { key: "rush_transport", label: "Rush Transport", format: (value: any) => formatCurrency(value) },
      { key: "socials_ads", label: "Social Ads", format: (value: any) => formatCurrency(value) },
      { key: "promotion_customer", label: "Promotion Customer", format: (value: any) => formatCurrency(value) },
      {
        key: "bonus_insurance_car_life_engine",
        label: "Bonus Insurance",
        format: (value: any) => formatCurrency(value),
      },
    ],
  },
  {
    title: "Maintenance Costs",
    properties: [
      { key: "fuel_cost", label: "Fuel Cost", format: (value: any) => formatCurrency(value) },
      { key: "parking_cost", label: "Parking Cost", format: (value: any) => formatCurrency(value) },
      { key: "liquidor_cost", label: "Liquidor Cost", format: (value: any) => formatCurrency(value) },
      { key: "engine_repair_cost", label: "Engine Repair", format: (value: any) => formatCurrency(value) },
      { key: "suspension_repair_cost", label: "Suspension Repair", format: (value: any) => formatCurrency(value) },
      { key: "autopart_cost", label: "Autopart Cost", format: (value: any) => formatCurrency(value) },
      { key: "battery_cost", label: "Battery Cost", format: (value: any) => formatCurrency(value) },
      { key: "tires_wheels_cost", label: "Tires & Wheels", format: (value: any) => formatCurrency(value) },
      {
        key: "repair_and_management_cost",
        label: "Total Repair & Management",
        format: (value: any) => formatCurrency(value),
      },
    ],
  },
  {
    title: "Profit Calculations",
    properties: [
      { key: "payment_fee", label: "Payment Fee", format: (value: any) => formatCurrency(value) },
      { key: "init_profit", label: "Initial Profit", format: (value: any) => formatCurrency(value) },
      { key: "deduct_transfer_twelve", label: "Deduct Transfer (12%)", format: (value: any) => formatCurrency(value) },
      {
        key: "profit_and_loss",
        label: "Profit/Loss",
        format: (value: any, item: any) => (
          <span className={item.profit_and_loss >= 0 ? "text-green-600 font-medium" : "text-red-600 font-medium"}>
            {formatCurrency(value)}
          </span>
        ),
      },
      {
        key: "profit_and_loss_percent",
        label: "Profit/Loss %",
        format: (value: any, item: any) => (
          <span
            className={item.profit_and_loss_percent >= 0 ? "text-green-600 font-medium" : "text-red-600 font-medium"}
          >
            {formatPercent(value)}
          </span>
        ),
      },
    ],
  },
  {
    title: "Additional Information",
    properties: [
      { key: "bank", label: "Bank", format: (value: any) => value },
      { key: "salesperson", label: "Salesperson", format: (value: any) => value },
    ],
  },
]

export function MonthlySummaryTable({ data, isLoading }: MonthlySummaryTableProps) {
  if (isLoading) {
    return (
      <div className="h-[600px] flex items-center justify-center">
        <div className="animate-pulse text-muted-foreground">Loading monthly summary data...</div>
      </div>
    )
  }

  if (data.length === 0) {
    return (
      <div className="h-[600px] flex items-center justify-center">
        <div className="text-muted-foreground">No cars were sold during this period.</div>
      </div>
    )
  }



  return (
    <div>
      <ScrollArea className="h-[calc(100vh-250px)]">
        <div className="overflow-x-auto">
          <Table className="w-auto border-collapse">
            <TableHeader className="sticky top-0 bg-background z-20">
              <TableRow>
                <TableHead className="sticky left-0 bg-background z-30 whitespace-nowrap text-sm py-1 px-3 border-r border-gray-200">
                  Property
                </TableHead>
                {data.map((car) => (
                  <TableHead
                    key={car.summary_id}
                    className="text-center whitespace-nowrap text-sm py-1 px-2 border-r border-gray-200"
                  >
                    Car #{car.car_number}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {propertyGroups.map((group) => {
                // Assign different background colors based on the group title
                let bgColorClass = ""
                switch (group.title) {
                  case "Basic Information":
                    bgColorClass = "bg-blue-50"
                    break
                  case "Revenue":
                    bgColorClass = "bg-green-50"
                    break
                  case "Investment":
                    bgColorClass = "bg-amber-50"
                    break
                  case "Tax":
                    bgColorClass = "bg-purple-50"
                    break
                  case "Commission and Fees":
                    bgColorClass = "bg-indigo-50"
                    break
                  case "Maintenance Costs":
                    bgColorClass = "bg-rose-50"
                    break
                  case "Profit Calculations":
                    bgColorClass = "bg-emerald-50"
                    break
                  case "Additional Information":
                    bgColorClass = "bg-sky-50"
                    break
                  default:
                    bgColorClass = "bg-gray-50"
                }

                return (
                  <React.Fragment key={`group-${group.title}`}>
                    {/* Group Header */}
                    <TableRow className={bgColorClass}>
                      <TableCell
                        colSpan={data.length + 1}
                        className={`sticky left-0 font-bold py-1 px-3 text-sm ${bgColorClass} border-r border-gray-200 whitespace-nowrap`}
                      >
                        {group.title}
                      </TableCell>
                    </TableRow>

                    {/* Group Properties */}
                    {group.properties.map((prop) => (
                      <TableRow key={prop.key} className="hover:bg-muted/20">
                        <TableCell className="sticky left-0 bg-background font-medium py-1 px-3 text-sm z-10 border-r border-gray-200 whitespace-nowrap">
                          {prop.label}
                        </TableCell>
                        {data.map((car) => (
                          <TableCell
                            key={`${car.summary_id}-${prop.key}`}
                            className="text-right py-1 px-2 text-sm border-r border-gray-200 whitespace-nowrap"
                          >
                            {prop.format(car[prop.key as keyof MonthlySummary], car)}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </React.Fragment>
                )
              })}
            </TableBody>
          </Table>
        </div>
      </ScrollArea>
    </div>
  )
}
