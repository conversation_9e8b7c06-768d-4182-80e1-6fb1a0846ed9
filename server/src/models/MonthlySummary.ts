export interface MonthlySummaryParams {
  month: number
  year: number
}

export interface MonthlySummaryResponse {
  data: MonthlySummary[]
  totalCount: number
}

// Monthly Summary Interface - matches the frontend type
export interface MonthlySummary {
  // Unique identifier for the summary record
  summary_id: string

  // Car identification
  car_id: string
  car_number: number // Sequential number for cars sold in the month

  // Basic information (from database)
  purchase_date: string // From CarBuyin
  sale_date: string // From CarSellout
  tank_number: string // From CarBuyin
  brand: string // From CarBuyin
  vat_percent: number // From CarBuyin

  // Time calculations
  time_in_stock: number // Days: sale_date - purchase_date

  // Revenue (from database and calculations)
  actual_selling_price: number // From CarSellout
  car_amount_finance: number // From CarFinance
  vat_on_car_amount: number // From CarFinance
  car_commission_amount: number // From CarFinance
  total_revenue: number // Calculated: actual_selling_price + car_commission_amount

  // Investment costs (from database)
  purchase_price: number // From CarBuyin
  purchase_vat_percent: number // From CarBuyin
  operation_cost_incl_vat: number // From CarBuyin
  transport_1_auction_lot: number // From CarBuyin
  initial_check: number // From CarBuyin
  tax_insurance_cost_zero: number // From CarBuyin
  other_costs_seven: number // From CarBuyin
  five_three_tax_percentage: number // From CarBuyin
  qc1_auction_lot: number // From CarBuyin
  transport_2_personal_payment: number // From CarBuyin
  transport_3_tl_payment: number // From CarBuyin
  total_investment: number // From CarStockInfo

  // Tax information
  thirty_document: number // Calculated
  
  // Commission and fees (from database)
  commission_s: number // From CarSellout
  commission_agent: number // From CarSellout
  commission_manager: number // From CarSellout
  qc_siriporn: number // From CarBuyin (qc3)
  miscellaneous_siriporn: number // From CarBuyin
  marketing_bak_cost: number // From CarBuyin
  finance_payment_process: number // From CarFinance
  ems_registration_qc3: number // From CarBuyin
  registration_fee: number // From CarBuyin
  rush_transport: number // From CarBuyin
  socials_ads: number // From CarBuyin
  promotion_customer: number // From CarFinance
  bonus_insurance_car_life_engine: number // From CarFinance

  // Maintenance costs
  fuel_cost: number // time_in_stock in months * 500
  parking_cost: number // time_in_stock in months * 500
  liquidor_cost: number // Fixed rate: 500
  engine_repair_cost: number // From CarRepair
  suspension_repair_cost: number // From CarRepair
  autopart_cost: number // From CarRepair
  battery_cost: number // From CarRepair
  tires_wheels_cost: number // From CarRepair

  // Aggregated costs
  repair_and_management_cost: number // Sum of various costs

  // Profit calculations
  payment_fee: number // Fixed rate: 50
  init_profit: number // total_revenue - total_investment - repair_and_management_cost - payment_fee
  deduct_transfer_twelve: number // Calculated based on time_in_stock
  profit_and_loss: number // init_profit - deduct_transfer_twelve
  profit_and_loss_percent: number // (profit_and_loss / total_investment) * 100

  // Additional information
  bank: string // From CarFinance
  salesperson: string // From CarFinance
}

// Raw data interface for database queries
export interface MonthlySummaryRawData {
  // Car stock info
  car_id: string
  index_number: number
  total_investment: number | null
  car_status: string

  // Car buyin data
  purchase_date: string | null
  tank_number: string | null
  brand: string | null
  vat_percent: number | null
  purchase_price: number | null
  purchase_vat_percent: number | null
  operation_cost_incl_vat: number | null
  transport_1_auction_lot: number | null
  initial_check: number | null
  tax_insurance_cost_zero: number | null
  other_costs_seven: number | null
  five_three_tax_percentage: number | null
  qc1_auction_lot: number | null
  transport_2_personal_payment: number | null
  transport_3_tl_payment: number | null
  qc3: number | null
  ems_registration_qc3: number | null
  registration_fee: number | null

  // Car sellout data
  sale_date: string | null
  actual_selling_price: number | null
  commission_s: number | null
  commission_agent: number | null
  commission_manager: number | null

  // Car finance data
  car_amount: number | null
  car_vat_amount: number | null
  car_commission_amount: number | null
  promotion_customer: number | null
  bonus_insurance_car_life_engine: number | null
  bank: string | null
  salesperson: string | null

  // Car repair data
  engine_repair_cost: number | null
  suspension_repair_cost: number | null
  autopart_cost: number | null
  battery_cost: number | null
  tires_wheels_cost: number | null
}
