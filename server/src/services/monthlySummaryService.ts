import MonthlySummaryRepository from '../repositories/monthlySummaryRepository'
import { MonthlySummaryParams, MonthlySummaryResponse, MonthlySummary, MonthlySummaryRawData } from '../models/MonthlySummary'
import { v4 as uuidv4 } from 'uuid'

class MonthlySummaryService {
  private monthlySummaryRepository: MonthlySummaryRepository

  constructor(monthlySummaryRepository: MonthlySummaryRepository) {
    this.monthlySummaryRepository = monthlySummaryRepository
  }

  async getMonthlySummaryData(params: MonthlySummaryParams): Promise<MonthlySummaryResponse> {
    try {
      console.log(`[MonthlySummaryService.getMonthlySummaryData] 🔍 Processing request for month: ${params.month}, year: ${params.year}`)

      // Get raw data from repository
      const rawData = await this.monthlySummaryRepository.getMonthlySummaryData(params)
      
      if (rawData.length === 0) {
        return {
          data: [],
          totalCount: 0
        }
      }

      // Process and calculate monthly summary data
      const processedData = this.processMonthlySummaryData(rawData)

      console.log(`[MonthlySummaryService.getMonthlySummaryData] ✅ Successfully processed ${processedData.length} monthly summary records`)

      return {
        data: processedData,
        totalCount: processedData.length
      }
    } catch (error: any) {
      console.error("[MonthlySummaryService.getMonthlySummaryData] ❌ Error:", error.message)
      throw error
    }
  }

  private processMonthlySummaryData(rawData: MonthlySummaryRawData[]): MonthlySummary[] {
    return rawData.map((raw, index) => {
      // Calculate time in stock (days)
      const timeInStock = this.calculateTimeInStock(raw.purchase_date, raw.sale_date)
      
      // Calculate maintenance costs based on time in stock
      const timeInStockMonths = Math.ceil(timeInStock / 30) // Convert days to months
      const fuelCost = timeInStockMonths * 500
      const parkingCost = timeInStockMonths * 500
      const liquidorCost = 500 // Fixed rate

      // Calculate revenue
      const actualSellingPrice = raw.actual_selling_price || 0
      const carAmountFinance = raw.car_amount || 0
      const vatOnCarAmount = raw.car_vat_amount || 0
      const carCommissionAmount = raw.car_commission_amount || 0
      const totalRevenue = actualSellingPrice + carCommissionAmount

      // Calculate investment costs
      const purchasePrice = raw.purchase_price || 0
      const operationCostInclVat = raw.operation_cost_incl_vat || 0
      const transport1AuctionLot = raw.transport_1_auction_lot || 0
      const initialCheck = raw.initial_check || 0
      const taxInsuranceCostZero = raw.tax_insurance_cost_zero || 0
      const otherCostsSeven = raw.other_costs_seven || 0
      const fiveThreeTaxPercentage = raw.five_three_tax_percentage || 0
      const qc1AuctionLot = raw.qc1_auction_lot || 0
      const transport2PersonalPayment = raw.transport_2_personal_payment || 0
      const transport3TlPayment = raw.transport_3_tl_payment || 0
      const totalInvestment = raw.total_investment || 0

      // Calculate commission and fees
      const commissionS = raw.commission_s || 0
      const commissionAgent = raw.commission_agent || 0
      const commissionManager = raw.commission_manager || 0
      const qcSiriporn = raw.qc3 || 0
      const emsRegistrationQc3 = raw.ems_registration_qc3 || 0
      const registrationFee = raw.registration_fee || 0
      const promotionCustomer = raw.promotion_customer || 0
      const bonusInsuranceCarLifeEngine = raw.bonus_insurance_car_life_engine || 0

      // Calculate repair costs
      const engineRepairCost = raw.engine_repair_cost || 0
      const suspensionRepairCost = raw.suspension_repair_cost || 0
      const autopartCost = raw.autopart_cost || 0
      const batteryCost = raw.battery_cost || 0
      const tiresWheelsCost = raw.tires_wheels_cost || 0

      // Calculate aggregated repair and management cost
      const repairAndManagementCost = commissionS + commissionAgent + commissionManager +
        emsRegistrationQc3 + qcSiriporn + registrationFee + promotionCustomer +
        bonusInsuranceCarLifeEngine + liquidorCost + engineRepairCost +
        suspensionRepairCost + autopartCost + batteryCost + tiresWheelsCost

      // Calculate profit
      const paymentFee = 50 // Fixed rate
      const initProfit = totalRevenue - totalInvestment - repairAndManagementCost - paymentFee
      const deductTransferTwelve = this.calculateDeductTransfer(totalInvestment, timeInStock)
      const profitAndLoss = initProfit - deductTransferTwelve
      const profitAndLossPercent = totalInvestment > 0 ? (profitAndLoss / totalInvestment) * 100 : 0

      // Calculate 30% document (example calculation)
      const thirtyDocument = totalInvestment * 0.3

      const monthlySummary: MonthlySummary = {
        summary_id: uuidv4(),
        car_id: raw.car_id,
        car_number: index + 1, // Sequential number for cars sold in the month

        // Basic information
        purchase_date: raw.purchase_date || '',
        sale_date: raw.sale_date || '',
        tank_number: raw.tank_number || '',
        brand: raw.brand || '',
        vat_percent: raw.vat_percent || 0,
        time_in_stock: timeInStock,

        // Revenue
        actual_selling_price: actualSellingPrice,
        car_amount_finance: carAmountFinance,
        vat_on_car_amount: vatOnCarAmount,
        car_commission_amount: carCommissionAmount,
        total_revenue: totalRevenue,

        // Investment
        purchase_price: purchasePrice,
        purchase_vat_percent: raw.purchase_vat_percent || 0,
        operation_cost_incl_vat: operationCostInclVat,
        transport_1_auction_lot: transport1AuctionLot,
        initial_check: initialCheck,
        tax_insurance_cost_zero: taxInsuranceCostZero,
        other_costs_seven: otherCostsSeven,
        five_three_tax_percentage: fiveThreeTaxPercentage,
        qc1_auction_lot: qc1AuctionLot,
        transport_2_personal_payment: transport2PersonalPayment,
        transport_3_tl_payment: transport3TlPayment,
        total_investment: totalInvestment,

        // Tax
        thirty_document: thirtyDocument,

        // Commission and fees
        commission_s: commissionS,
        commission_agent: commissionAgent,
        commission_manager: commissionManager,
        qc_siriporn: qcSiriporn,
        miscellaneous_siriporn: 0, // Not available in current schema
        marketing_bak_cost: 0, // Not available in current schema
        finance_payment_process: 0, // Not available in current schema
        ems_registration_qc3: emsRegistrationQc3,
        registration_fee: registrationFee,
        rush_transport: 0, // Not available in current schema
        socials_ads: 0, // Not available in current schema
        promotion_customer: promotionCustomer,
        bonus_insurance_car_life_engine: bonusInsuranceCarLifeEngine,

        // Maintenance costs
        fuel_cost: fuelCost,
        parking_cost: parkingCost,
        liquidor_cost: liquidorCost,
        engine_repair_cost: engineRepairCost,
        suspension_repair_cost: suspensionRepairCost,
        autopart_cost: autopartCost,
        battery_cost: batteryCost,
        tires_wheels_cost: tiresWheelsCost,
        repair_and_management_cost: repairAndManagementCost,

        // Profit calculations
        payment_fee: paymentFee,
        init_profit: initProfit,
        deduct_transfer_twelve: deductTransferTwelve,
        profit_and_loss: profitAndLoss,
        profit_and_loss_percent: profitAndLossPercent,

        // Additional information
        bank: raw.bank || '',
        salesperson: raw.salesperson || '',
      }

      return monthlySummary
    })
  }

  private calculateTimeInStock(purchaseDate: string | null, saleDate: string | null): number {
    if (!purchaseDate || !saleDate) return 0
    
    const purchase = new Date(purchaseDate)
    const sale = new Date(saleDate)
    const diffTime = Math.abs(sale.getTime() - purchase.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  private calculateDeductTransfer(totalInvestment: number, timeInStock: number): number {
    // Calculate 12% deduction based on time in stock
    // This is a simplified calculation - adjust based on business rules
    const monthsInStock = timeInStock / 30
    const deductionRate = 0.12 // 12%
    return (totalInvestment * deductionRate * monthsInStock) / 12
  }
}

export default MonthlySummaryService
