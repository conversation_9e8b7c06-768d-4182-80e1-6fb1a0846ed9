import express from 'express'
import cors from 'cors'
import authRoutes from './api/auth'
import itemsRoutes from './api/car'
import reportsRoutes from './api/reports'
import monthlyTaxRoutes from './api/monthlyTax'
import monthlySummaryRoutes from './api/monthlySummary'

import dotenv from 'dotenv'
import path from 'path'
// dotenv.config({ path: '../.env.local' })
dotenv.config({
    path: path.resolve(__dirname, '../.env.local') // ensures the correct path
})

const app = express()
const allowedOrigins = (process.env.CORS_ORIGINS || '').split(',')

// MIDDLEWARE
app.use(cors({
    origin: function (origin, callback) {
        if (!origin) return callback(null, true)

        const isAllowed = allowedOrigins.includes(origin) || origin.endsWith('.vercel.app')
        if (isAllowed) {
            callback(null, true)
        } else {
            console.warn(`❌ Blocked by CORS: ${origin}`)
            callback(new Error('Not allowed by CORS'))
        }
    },
    credentials: true
}))

app.use(express.json())

// ROUTES
app.use('/auth', authRoutes)
app.use('/items', itemsRoutes)
app.use('/reports', reportsRoutes)
app.use('/api/reports', monthlyTaxRoutes)
app.use('/api/reports', monthlySummaryRoutes)

const PORT = process.env.PORT || 4000
app.listen(PORT, () => console.log(`🚀 Server running on http://localhost:${PORT}`))
