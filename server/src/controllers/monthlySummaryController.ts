import { Request, Response } from "express"
import MonthlySummaryService from "../services/monthlySummaryService"
import { MonthlySummaryParams } from "../models/MonthlySummary"

class MonthlySummaryController {
  private monthlySummaryService: MonthlySummaryService

  constructor(monthlySummaryService: MonthlySummaryService) {
    this.monthlySummaryService = monthlySummaryService
  }

  async getMonthlySummaryData(req: Request, res: Response) {
    try {
      const { month, year } = req.query
      console.log("CHECK QUERY MONTH AND YEAR", month, year)

      // Validate required parameters
      if (!month || !year) {
        return res.status(400).json({ error: 'Month and year are required parameters' })
      }

      // Parse parameters
      const params: MonthlySummaryParams = {
        month: parseInt(month as string),
        year: parseInt(year as string)
      }

      // Validate parameter values
      console.log(`[MonthlySummaryController.getMonthlySummaryData] 📊 Processing request for month: ${params.month}, year: ${params.year}`)

      const data = await this.monthlySummaryService.getMonthlySummaryData(params)
      
      console.log(`[MonthlySummaryController.getMonthlySummaryData] ✅ Successfully returned ${data.totalCount} monthly summary records`)
      
      return res.status(200).json(data)
    } catch (error: any) {
      console.error("[MonthlySummaryController.getMonthlySummaryData] ❌ Error:", error.message)
      return res.status(500).json({ error: error.message || 'Failed to fetch monthly summary data' })
    }
  }
}

export default MonthlySummaryController
