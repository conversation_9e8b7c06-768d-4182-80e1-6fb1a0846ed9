import { supabase } from '../config/database'
import { MonthlySummaryParams, MonthlySummaryRawData } from '../models/MonthlySummary'

class MonthlySummaryRepository {
  async getMonthlySummaryData(params: MonthlySummaryParams): Promise<MonthlySummaryRawData[]> {
    const { month, year } = params

    // Create date range for the selected month
    const startDate = `${year}-${month.toString().padStart(2, '0')}-01`

    // Calculate the last day of the month correctly
    const nextMonth = month === 12 ? 1 : month + 1
    const nextYear = month === 12 ? year + 1 : year
    const lastDay = new Date(nextYear, nextMonth - 1, 0).getDate()
    const endDate = `${year}-${month.toString().padStart(2, '0')}-${lastDay.toString().padStart(2, '0')}`

    console.log(`[MonthlySummaryRepository.getMonthlySummaryData] 📅 Querying sold cars for period: ${startDate} to ${endDate}`)

    // Query cars that were sold in the specified month/year with status 'sold'
    const { data: soldCarsData, error: soldCarsError } = await supabase
      .from('car_stock_info')
      .select(`
        car_id,
        index_number,
        total_investment,
        car_status,
        car_buyin (
          purchase_date,
          tank_number,
          brand,
          vat_percent,
          purchase_price,
          purchase_vat_percent,
          operation_cost_incl_vat,
          transport_1_auction_lot,
          initial_check,
          tax_insurance_cost_zero,
          other_costs_seven,
          five_three_tax_percentage,
          qc1_auction_lot,
          transport_2_personal_payment,
          transport_3_tl_payment,
          qc3,
          ems_registration_qc3,
          registration_fee
        ),
        car_sellout (
          sale_date,
          actual_selling_price,
          commission_s,
          commission_agent,
          commission_manager
        ),
        car_finance (
          car_amount,
          car_vat_amount,
          car_commission_amount,
          promotion_customer,
          bonus_insurance_car_life_engine,
          bank,
          salesperson
        ),
        car_repair (
          engine_repair_cost,
          suspension_repair_cost,
          autopart_cost,
          battery_cost,
          tires_wheels_cost
        )
      `)
      .eq('car_status', 'sold')
      .gte('car_sellout.sale_date', startDate)
      .lte('car_sellout.sale_date', endDate)
      .not('car_sellout.sale_date', 'is', null)

    if (soldCarsError) {
      console.error("[MonthlySummaryRepository.getMonthlySummaryData] ❌ Error fetching sold cars data:", soldCarsError.message)
      throw soldCarsError
    }

    if (!soldCarsData || soldCarsData.length === 0) {
      console.log("[MonthlySummaryRepository.getMonthlySummaryData] ℹ️ No sold cars found for the specified period")
      return []
    }

    console.log(`[MonthlySummaryRepository.getMonthlySummaryData] ✅ Found ${soldCarsData.length} sold cars`)

    // Transform the nested data structure to flat structure
    const transformedData: MonthlySummaryRawData[] = soldCarsData.map((car: any) => {
      const buyin = car.car_buyin || {}
      const sellout = car.car_sellout || {}
      const finance = car.car_finance || {}
      const repair = car.car_repair || {}

      return {
        // Car stock info
        car_id: car.car_id,
        index_number: car.index_number,
        total_investment: car.total_investment,
        car_status: car.car_status,

        // Car buyin data
        purchase_date: buyin.purchase_date,
        tank_number: buyin.tank_number,
        brand: buyin.brand,
        vat_percent: buyin.vat_percent,
        purchase_price: buyin.purchase_price,
        purchase_vat_percent: buyin.purchase_vat_percent,
        operation_cost_incl_vat: buyin.operation_cost_incl_vat,
        transport_1_auction_lot: buyin.transport_1_auction_lot,
        initial_check: buyin.initial_check,
        tax_insurance_cost_zero: buyin.tax_insurance_cost_zero,
        other_costs_seven: buyin.other_costs_seven,
        five_three_tax_percentage: buyin.five_three_tax_percentage,
        qc1_auction_lot: buyin.qc1_auction_lot,
        transport_2_personal_payment: buyin.transport_2_personal_payment,
        transport_3_tl_payment: buyin.transport_3_tl_payment,
        qc3: buyin.qc3,
        ems_registration_qc3: buyin.ems_registration_qc3,
        registration_fee: buyin.registration_fee,

        // Car sellout data
        sale_date: sellout.sale_date,
        actual_selling_price: sellout.actual_selling_price,
        commission_s: sellout.commission_s,
        commission_agent: sellout.commission_agent,
        commission_manager: sellout.commission_manager,

        // Car finance data
        car_amount: finance.car_amount,
        car_vat_amount: finance.car_vat_amount,
        car_commission_amount: finance.car_commission_amount,
        promotion_customer: finance.promotion_customer,
        bonus_insurance_car_life_engine: finance.bonus_insurance_car_life_engine,
        bank: finance.bank,
        salesperson: finance.salesperson,

        // Car repair data
        engine_repair_cost: repair.engine_repair_cost,
        suspension_repair_cost: repair.suspension_repair_cost,
        autopart_cost: repair.autopart_cost,
        battery_cost: repair.battery_cost,
        tires_wheels_cost: repair.tires_wheels_cost,
      }
    })

    return transformedData
  }
}

export default MonthlySummaryRepository
