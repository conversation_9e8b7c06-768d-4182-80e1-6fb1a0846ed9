import express from 'express'
import MonthlySummaryController from '../controllers/monthlySummaryController'
import MonthlySummaryService from '../services/monthlySummaryService'
import MonthlySummaryRepository from '../repositories/monthlySummaryRepository'
import { authMiddleware } from '../middleware/authMiddleware'

const router = express.Router()

// Initialize repository, service, and controller
const monthlySummaryRepository = new MonthlySummaryRepository()
const monthlySummaryService = new MonthlySummaryService(monthlySummaryRepository)
const monthlySummaryController = new MonthlySummaryController(monthlySummaryService)

// Monthly summary endpoint
router.route('/monthly-summary')
  .get(authMiddleware, monthlySummaryController.getMonthlySummaryData.bind(monthlySummaryController))

export default router
