import express from 'express';
import ReportController from '../controllers/reportController';
import ReportService from '../services/reportService';
import ReportRepository from '../repositories/reportRepository';
import { authMiddleware } from '../middleware/authMiddleware';

const router = express.Router();

const reportRepository = new ReportRepository();
const reportService = new ReportService(reportRepository);
const reportController = new ReportController(reportService);

// Daily auction summary endpoint
router.route('/daily-auction')
  .get(authMiddleware, reportController.getDailyAuctionSummary.bind(reportController));

// Add other report endpoints here as needed
// Monthly summary - now handled by separate monthlySummary.ts route
// router.route('/monthly-summary')
//   .get(authMiddleware, reportController.getMonthlySummary.bind(reportController));

// Sales commission
// router.route('/sales-commission')
//   .get(authMiddleware, reportController.getSalesCommission.bind(reportController));

// Monthly tax
// router.route('/monthly-tax')
//   .get(authMiddleware, reportController.getMonthlyTax.bind(reportController));

// Performance analytics
// router.route('/performance')
//   .get(authMiddleware, reportController.getPerformance.bind(reportController));

export default router;
